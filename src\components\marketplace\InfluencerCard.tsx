'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Star } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import type { InfluencerSearchResult } from '@/lib/marketplace';

interface InfluencerCardProps {
  influencer: InfluencerSearchResult;
}

export function InfluencerCard({ influencer }: InfluencerCardProps) {
  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} KM`;
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Rezervisano mjesto - uvijek 3 platforme
  const displayPlatforms = [
    ...influencer.platforms.slice(0, 3),
    ...Array(Math.max(0, 3 - influencer.platforms.length)).fill(null)
  ];

  // Rezervisano mjesto - uvijek 2 paketa
  const displayPricing = [
    ...influencer.pricing.slice(0, 2),
    ...Array(Math.max(0, 2 - influencer.pricing.length)).fill(null)
  ];

  // Glavna kategorija za prikaz
  const mainCategory = influencer.categories?.[0]?.name || 'Influencer';

  return (
    <Link href={`/influencer/${influencer.username}`}>
      <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl group hover:shadow-lg transition-all duration-200">
        <Card className="w-full overflow-hidden bg-white h-full">
          <CardContent className="p-0">
            {/* Image section */}
            <div className="relative aspect-[4/3] max-h-48">
              <Image
                src={influencer.avatar_url || "/placeholder.svg"}
                alt={`${influencer.full_name || influencer.username} profile`}
                fill
                className="object-cover"
              />

              {/* Verified badge - top left */}
              {influencer.is_verified && (
                <div className="absolute top-3 left-3 z-10">
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1 shadow-lg"
                  >
                    <CheckCircle className="w-3 h-3 mr-1 fill-current" />
                    Verified
                  </Badge>
                </div>
              )}

              {/* Star rating - top right */}
              <div className="absolute top-3 right-3 z-10">
                <Badge variant="secondary" className="bg-black/80 text-white border-0 px-2 py-1 shadow-lg">
                  <Star className="w-3 h-3 mr-1 fill-yellow-400 text-yellow-400" />
                  {influencer.average_rating?.toFixed(1) || '0.0'}
                </Badge>
              </div>

              {/* Category badge - bottom left */}
              <div className="absolute bottom-3 left-3 z-10">
                <Badge variant="outline" className="bg-white/90 text-xs shadow-sm">
                  {mainCategory}
                </Badge>
              </div>
            </div>

            {/* Info section */}
            <div className="p-4 space-y-3">
              {/* Username and location */}
              <div className="text-left">
                <h3 className="text-sm font-semibold group-hover:text-primary transition-colors">
                  {influencer.full_name || influencer.username}
                </h3>
                <p className="text-xs text-gray-500">
                  @{influencer.username}
                  {influencer.location && ` • ${influencer.location}`}
                </p>
              </div>

              {/* Bio */}
              {influencer.bio && (
                <p className="text-xs text-gray-600 line-clamp-2">{influencer.bio}</p>
              )}

              {/* Social handles with follower counts */}
              <div className="space-y-2">
                {displayPlatforms.slice(0, 2).map((platform, index) => (
                  platform ? (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                          <span className="text-white text-xs">{platform.platform_icon}</span>
                        </div>
                        <span className="truncate max-w-[100px]">{platform.handle}</span>
                      </div>
                      <span className="text-xs font-medium text-gray-700">
                        {formatFollowers(platform.followers_count)}
                      </span>
                    </div>
                  ) : (
                    <div key={index} className="flex items-center justify-between opacity-30">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <div className="p-1 bg-gray-200 rounded-full">
                          <span className="text-gray-400 text-xs">📱</span>
                        </div>
                        <span>-</span>
                      </div>
                      <span className="text-xs text-gray-400">-</span>
                    </div>
                  )
                ))}
              </div>

              {/* Separator */}
              <hr className="border-gray-200" />

              {/* Packages */}
              <div className="space-y-2">
                <h4 className="text-xs font-semibold text-gray-700">Paketi</h4>
                {displayPricing.slice(0, 2).map((pkg, index) => (
                  pkg ? (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 truncate max-w-[120px]">
                        {pkg.content_type_name}
                      </span>
                      <span className="text-xs font-medium text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                        {formatPrice(pkg.price)}
                      </span>
                    </div>
                  ) : (
                    <div key={index} className="flex justify-between items-center opacity-30">
                      <span className="text-xs text-gray-400">-</span>
                      <span className="text-xs text-gray-400">-</span>
                    </div>
                  )
                ))}
              </div>

              {/* Price Range */}
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Od:</span>
                  <span className="text-sm font-bold text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                    {influencer.min_price ? formatPrice(influencer.min_price) : 'Na upit'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Link>
  );
}
