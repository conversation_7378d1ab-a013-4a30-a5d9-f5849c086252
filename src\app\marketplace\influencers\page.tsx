'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Search, Filter, MapPin, Users, Star, Verified } from 'lucide-react';
import {
  searchInfluencers,
  getMarketplaceStats,
  type InfluencerSearchResult,
  type SearchFilters,
} from '@/lib/marketplace';
import HorizontalFilters from '@/components/marketplace/horizontal-filters';
import { TooltipProvider } from '@/components/ui/tooltip';
import Link from 'next/link';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Rating } from '@/components/ui/rating';
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';

export default function InfluencerMarketplacePage() {
  const [influencers, setInfluencers] = useState<InfluencerSearchResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    sortBy: 'relevance',
    limit: 12,
    offset: 0,
  });

  // Load initial data
  useEffect(() => {
    loadInfluencers();
  }, []);

  const loadInfluencers = async (newFilters?: SearchFilters) => {
    setLoading(true);
    try {
      const filtersToUse = newFilters || filters;
      const { data, error } = await searchInfluencers(filtersToUse);

      console.log('Search result:', { data, error });

      if (error) {
        console.error('Error loading influencers:', error);
        return;
      }

      console.log('Setting influencers:', data?.length || 0);
      setInfluencers(data || []);
    } catch (error) {
      console.error('Error in loadInfluencers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    const newFilters = {
      ...filters,
      search: searchQuery,
      offset: 0,
    };
    setFilters(newFilters);
    loadInfluencers(newFilters);
  };

  const handleSortChange = (sortBy: string) => {
    const newFilters = {
      ...filters,
      sortBy: sortBy as any,
      offset: 0,
    };
    setFilters(newFilters);
    loadInfluencers(newFilters);
  };

  const formatPrice = (price: number) => {
    return `${price} KM`;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <DashboardLayout requiredUserType="business">
      <TooltipProvider>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">
              Marketplace Influencera
            </h1>
            <p className="text-muted-foreground mt-1">
              Pronađite savršenog influencera za vašu kampanju
            </p>
          </div>

          <div>
            {/* Horizontal Filters */}
            <HorizontalFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearFilters={() => setFilters({})}
            />

            {/* Search and Sort Bar */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Pretražite influencere po imenu ili username..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyDown={e => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={filters.sortBy}
                  onChange={e => handleSortChange(e.target.value)}
                  className="px-3 py-2 border rounded-md bg-background min-w-[180px]"
                >
                  <option value="relevance">Relevantnost</option>
                  <option value="price_asc">Cijena (najniža)</option>
                  <option value="price_desc">Cijena (najviša)</option>
                  <option value="followers_desc">Broj pratilaca</option>
                  <option value="newest">Najnoviji</option>
                </select>
                <Button onClick={handleSearch}>
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Results */}
            <div className="w-full">
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-16 h-16 bg-muted rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-muted rounded mb-2"></div>
                            <div className="h-3 bg-muted rounded w-2/3"></div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-3 bg-muted rounded"></div>
                          <div className="h-3 bg-muted rounded w-3/4"></div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : influencers.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                      Nema rezultata
                    </h3>
                    <p className="text-muted-foreground">
                      Pokušajte sa drugačijim kriterijumima pretrage.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">
                      {influencers.length} influencer
                      {influencers.length !== 1 ? 'a' : ''}
                    </h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {influencers.map(influencer => (
                      <InfluencerCard key={influencer.id} influencer={influencer} />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
